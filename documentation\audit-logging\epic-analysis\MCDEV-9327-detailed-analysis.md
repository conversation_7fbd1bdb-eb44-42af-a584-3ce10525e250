# MCDEV-9327 Epic Analysis: Audit Log Subscription Setup Changes

## Epic Overview
**Epic ID**: MCDEV-9327  
**Title**: Audit Log Subscription Setup Changes  
**Business Rationale**: Provide comprehensive audit trails for all subscription system configuration changes to support compliance, troubleshooting, and administrative oversight of membership subscription management.

## Child Stories Implementation Analysis

### MCDEV-9329: Audit Log Subscription Settings
**Implementation Date**: January 2025  
**Files Modified**:
- `database/membercentral/migrations/2025/2025-01/MCDEV-9329 - Audit Log Subscription Settings.sql`
- `database/membercentral/schema/memberCentral/procedures/sub_insertAuditLog.sql`
- `models/system/platform/mongo/auditLog_SUBSETUP.cfc`

**Technical Implementation**:
- **AUDITCODE**: "SUBSETUP"
- **Collection**: Specialized `auditLog_SUBSETUP` collection
- **Unique Feature**: SUBKEYMAP JSON field for subscription context data
- **Stored Procedure Created**: `sub_insertAuditLog`

**SUBKEYMAP Usage Patterns**:
- `{"TYPEID": 123}`: Subscription type operations
- `{"SUBSCRIPTIONID": 456, "SUBSCRIPTIONTYPEID": 123}`: Subscription operations
- `{"SETID": 789}`: Subscription set operations
- `{"SCHEDULEID": 101}`: Rate schedule operations
- `{"RATEID": 202}`: Rate operations

**Implementation Pattern**:
```sql
CREATE PROCEDURE dbo.sub_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@msgjson varchar(max),
@subKeyMapJSON varchar(max),
@isImport bit = 0,
@enteredByMemberID int

IF @isImport = 1
    SET @msgjson = '[Import] ' + @msgjson;

INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
VALUES ('{ "c":"auditLog_SUBSETUP", "d": {
    "AUDITCODE":"SUBSETUP",
    "AREACODE":"' + @areaCode + '",
    "ORGID":' + cast(@orgID as varchar(10)) + ',
    "SITEID":' + cast(@siteID as varchar(10)) + ',
    "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
    "ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
    "MESSAGE":"' + @msgjson + '",
    "SUBKEYMAP":' + @subKeyMapJSON + ' } }');
```

### MCDEV-9330: Show Subscription Setup Changes in Admin Navigation
**Implementation Date**: January 2025  
**Files Modified**:
- `database/membercentral/migrations/2025/2025-01/MCDEV-9330 - Show Subscription Setup Changes in a new admin navigation.sql`
- `membercentral/model/admin/subscriptions/dsp_auditLog.cfm`

**Technical Implementation**:
- **Admin Navigation**: Added "Audit Log" under Subscription management
- **CFC Method**: `getSubSetUpAuditLogs`
- **Interface**: DataTables with server-side processing
- **Filtering**: Date range and area-specific filtering

**AREACODE Categories**:
- `SUBTYPE`: Subscription type configurations
- `SUBSCRIPTION`: Individual subscription settings
- `SUBSET`: Subscription set management
- `RATESCH`: Rate schedule configurations
- `RATE`: Individual rate settings

### MCDEV-9333: Audit Log Subscription Changes
**Implementation Date**: February 2025  
**Files Modified**:
- `database/membercentral/migrations/2025/2025-02/MCDEV-9333 - Audit Log Subscription Changes.sql`

**Technical Implementation**:
- **AUDITCODE**: "SUBSETUP"
- **AREACODE**: "SUBSCRIPTION"
- **Integration Points**: Subscription CRUD operations
- **Advanced Features**: Bulk operations and import handling

**Audit Events Tracked**:
- Subscription creation with type association
- Subscription updates with change detection
- Subscription deletion with cascade information
- Subscription set reordering operations
- Import-based subscription modifications

**Subscription Deletion Pattern**:
```sql
DECLARE @subKeyMapJSON varchar(100) = '{ "SUBSCRIPTIONID":'+CAST(@subscriptionID AS varchar(10))+', "SUBSCRIPTIONTYPEID":'+CAST(@typeID AS VARCHAR(10))+' }';
SET @msgjson = 'Subscription ' + QUOTENAME(@subscriptionName) + ' under the Subscription Type ' + QUOTENAME(@subscriptionType) + ' has been deleted.';

EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBSCRIPTION', @msgjson=@msgjson,
        @subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
```

### MCDEV-9335: Audit Log Subscription Rates and Rate Schedules
**Implementation Date**: February 2025  
**Files Modified**:
- `database/membercentral/migrations/2025/2025-02/MCDEV-9335 - Audit Log Subscription Rates and Rate Schedules.sql`

**Technical Implementation**:
- **AUDITCODE**: "SUBSETUP"
- **AREACODE**: "RATESCH" and "RATE"
- **Advanced Change Detection**: Before/after comparison using `ams_getAuditLogMsg`
- **Complex Relationships**: Rate schedule and rate interdependencies

**Rate Schedule Update Pattern**:
```sql
-- Create temporary audit table
CREATE TABLE #tmpAuditLogData ([rowCode] varchar(10), [Rate Schedule Name] varchar(max), [API ID] varchar(max));

-- Capture old values
INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
SELECT 'OLDVAL', scheduleName, [uid]
FROM dbo.sub_rateSchedules
WHERE scheduleID = @checkScheduleID;

-- Capture new values
INSERT INTO #tmpAuditLogData ([rowCode], [Rate Schedule Name], [API ID])
SELECT 'NEWVAL', @scheduleName, @uid
FROM dbo.sub_rateSchedules
WHERE scheduleID = @checkScheduleID;

EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

IF ISNULL(@msg,'') <> '' BEGIN
    DECLARE @subKeyMapJSON varchar(100) = '{ "SCHEDULEID":'+CAST(@checkScheduleID AS varchar(10))+' }';
    SET @msg = 'Rate Schedule [' + @prevScheduleName + '] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;

    EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='RATESCH', @msgjson=@msg,
        @subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
END
```

### MCDEV-9336: Audit Log Subscription Setup Changes Made via Import
**Implementation Date**: February 2025  
**Files Modified**:
- `database/membercentral/migrations/2025/2025-02/MCDEV-9336 - Audit Log Subscription Setup Changes made via Import.sql`

**Technical Implementation**:
- **Import Flag**: `@isImport` parameter adds "[Import]" prefix
- **Bulk Operations**: Enhanced `ams_getAuditLogMsg` with `@isBulkUpdate` parameter
- **Complex Import Tracking**: Multi-entity import operations with consolidated audit logs

**Import Handling Pattern**:
```sql
IF @isImport = 1
    SET @msgjson = '[Import] ' + @msgjson;

-- For bulk import operations
EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogDataAO', @isBulkUpdate=1, @msg=@msgjson OUTPUT;

-- Consolidated import audit logging
EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode=@thisAreaCode, @msgjson=@msgjson,
    @subKeyMapJSON=@subKeyMapJSON, @isImport=1, @enteredByMemberID=@recordedByMemberID;
```

## Specialized MongoDB Collection Structure

### auditLog_SUBSETUP Collection Schema
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt,
    "AUDITCODE": "SUBSETUP",
    "AREACODE": String,
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String,
    "SUBKEYMAP": {  // Unique to SUBSETUP collection
        "TYPEID": NumberInt,
        "SUBSCRIPTIONID": NumberInt,
        "SUBSCRIPTIONTYPEID": NumberInt,
        "SETID": NumberInt,
        "SCHEDULEID": NumberInt,
        "RATEID": NumberInt
    }
}
```

## Admin Interface Implementation

### Subscription Audit Log Viewer
**File**: `membercentral/model/admin/subscriptions/dsp_auditLog.cfm`

**Features**:
- DataTables with server-side processing
- Date range filtering
- Area-specific filtering (SUBTYPE, SUBSCRIPTION, SUBSET, RATESCH, RATE)
- Import operation identification
- Real-time search capabilities

**Filter Options**:
```html
<select name="fArea" id="fArea" class="form-control">
    <option value="">All Areas</option>
    <option value="SUBTYPE">Subscription Type</option>
    <option value="SUBSCRIPTION">Subscription</option>
    <option value="RATE">Rate</option>
    <option value="RATESCH">Rate Schedule</option>
    <option value="SUBSET">Subscription Set</option>
</select>
```

### ColdFusion Integration
**File**: `membercentral/model/system/platform/auditLog.cfc`

```coldfusion
<cfset local.strRequest = {
    "c": "auditLog_SUBSETUP",
    "o": [{ k="_id", d="desc" }],
    "w": [],
    "l": arguments.limit
}>

<cfset arrayAppend(local.strRequest["w"], { k="SITEID", e="=", v=arguments.siteID })>
<cfset arrayAppend(local.strRequest["w"], { k="AUDITCODE", e="=", v="SUBSETUP" })>
<cfif len(arguments.area)>
    <cfset arrayAppend(local.strRequest["w"], { k="AREACODE", e="=", v=arguments.area })>
</cfif>
```

## Business Rationale Analysis

### Subscription Management Compliance
- **Revenue Tracking**: Monitor all changes to subscription pricing and structures
- **Regulatory Compliance**: Maintain audit trails for membership fee modifications
- **Financial Oversight**: Track rate schedule and pricing changes

### Operational Benefits
- **Troubleshooting**: Identify when subscription configuration issues were introduced
- **Change Management**: Track the impact of subscription modifications on member billing
- **Quality Assurance**: Monitor subscription setup and maintenance activities

### Import Operation Tracking
- **Bulk Change Monitoring**: Track large-scale subscription imports and updates
- **Data Integrity**: Ensure import operations are properly logged and traceable
- **Administrative Accountability**: Track who performed bulk subscription operations

## Technical Patterns Established

### SUBKEYMAP Contextual Data
- **Hierarchical Relationships**: Track parent-child relationships in subscription structures
- **Cross-Reference Tracking**: Maintain links between related subscription entities
- **Query Optimization**: Enable efficient filtering by subscription context

### Import Operation Handling
- **Import Flagging**: Distinguish between manual and import-based changes
- **Bulk Operation Consolidation**: Group related import changes into single audit entries
- **Import Source Tracking**: Maintain traceability of bulk operations

### Advanced Change Detection
- **Multi-Entity Updates**: Track changes across related subscription components
- **Cascade Impact Tracking**: Report dependent changes in subscription hierarchies
- **Complex Relationship Monitoring**: Track interdependencies between rates, schedules, and subscriptions
