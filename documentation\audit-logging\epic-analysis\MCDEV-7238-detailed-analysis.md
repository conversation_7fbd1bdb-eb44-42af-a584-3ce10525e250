# MCDEV-7238 Epic Analysis: Audit Referral Setup Changes

## Epic Overview
**Epic ID**: MCDEV-7238  
**Title**: Audit Referral Setup Changes  
**Business Rationale**: Provide comprehensive audit trails for all referral system configuration changes to support compliance, troubleshooting, and administrative oversight of the legal referral system.

## Child Stories Implementation Analysis

### MCDEV-7770: Create BER component to Audit Log Referral Settings Updates
**Implementation Date**: February 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-02/MCDEV-7387 - Create BER component to Audit Log Referral Settings Updates.sql`
- `database/membercentral/schema/memberCentral/procedures/ref_insertAuditLog.sql`
- `models/system/platform/mongo/auditLog_REF.cfc`

**Technical Implementation**:
- **AUDITCODE**: "REF"
- **Collection**: Specialized `auditLog_REF` collection
- **Unique Feature**: AREACODE parameter for granular categorization
- **Stored Procedure Created**: `ref_insertAuditLog`

**AREACODE Usage Patterns**:
- `PANELS`: Panel configuration changes
- `MAINSETTINGS`: Main referral system settings
- `FEETYPES`: Fee type configurations
- `CLASSIFICATIONS`: Client classification settings
- `AGENCIES`: Agency configurations
- `SOURCES`: Referral source settings
- `STATUSES`: Status configurations
- `SURVEYTYPES`: Survey type settings
- `LANGUAGES`: Language configurations
- `SCHEDJOBS`: Scheduled job configurations

**Implementation Pattern**:
```sql
CREATE PROCEDURE dbo.ref_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@msgjson varchar(max),
@enteredByMemberID int

INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
VALUES ('{ "c":"auditLog_REF", "d": {
    "AUDITCODE":"REF",
    "AREACODE":"' + @areaCode + '",
    "ORGID":' + cast(@orgID as varchar(10)) + ',
    "SITEID":' + cast(@siteID as varchar(10)) + ',
    "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
    "ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
    "MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
```

### MCDEV-7771: Log to AuditLog When Updating Panel Information
**Implementation Date**: March 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-03/MCDEV-7307 - Log to AuditLog When Updating Panel Information.sql`

**Technical Implementation**:
- **AUDITCODE**: "REF"
- **AREACODE**: "PANELS"
- **Enhanced Utility**: Extended `ams_getAuditLogMsg` procedure
- **Change Detection**: Before/after comparison using temporary tables

**Audit Events Tracked**:
- Panel name changes
- Status modifications
- Display settings updates
- Fee structure changes
- GL account assignments
- Parent panel relationships

**Advanced Change Detection Pattern**:
```sql
-- Create temporary audit table with OLDVAL/NEWVAL/DATATYPECODE rows
CREATE TABLE #tmpAuditLogData ([rowCode] varchar(10), [Status] varchar(max), [Display Panel in Front-End?] varchar(max));

-- Capture old values
INSERT INTO #tmpAuditLogData ([rowCode], [Status], [Display Panel in Front-End?])
SELECT 'OLDVAL', ps.statusName, p.feDspClientReferral
FROM dbo.ref_panels AS P
WHERE p.panelID = @panelID;

-- Capture new values  
INSERT INTO #tmpAuditLogData ([rowCode], [Status], [Display Panel in Front-End?])
SELECT 'NEWVAL', ps.statusName, @feDspClientReferral
FROM dbo.ref_panelStatus AS ps
WHERE ps.panelStatusID = @statusID;

-- Generate change message
EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
```

### MCDEV-7903: Log to AuditLog When Updating a Client Referral
**Implementation Date**: April 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-04/MCDEV-7380 - Log to AuditLog When Updating a Client Referral.sql`

**Technical Implementation**:
- **AUDITCODE**: "REF" 
- **AREACODE**: "CLIENTREFERRALS"
- **Advanced Feature**: JSON changes array output
- **Specialized Collection**: Uses `historyEntries_SYS_ADMIN_REFERRALUPDATE`

**Client Referral Audit Events**:
- Client information updates (name, contact details)
- Referral details changes (issue description, language preferences)
- Fee type modifications
- Counselor assignments
- Survey preferences updates
- Panel assignments

**JSON Changes Array Pattern**:
```sql
ALTER PROC dbo.ams_getAuditLogMsg
@auditLogTable varchar(60),
@outputAsChangesArray bit = 0,
@msg varchar(max) OUTPUT

-- When @outputAsChangesArray = 1, generates JSON array format:
-- [{"field":"First Name","oldValue":"John","newValue":"Jane"},{"field":"Email","oldValue":"<EMAIL>","newValue":"<EMAIL>"}]

EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @outputAsChangesArray=1, @msg=@msgjson OUTPUT;

IF ISNULL(@msgjson,'') <> '' BEGIN
    SET @msgjson = '[' + @msgjson + ']';
    EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
        @message=@mainMessage, @changesArray=@msgjson, @enteredByMemberID=@enteredByMemberID;
END
```

## Specialized MongoDB Collection Structure

### auditLog_REF Collection Schema
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt, 
    "AUDITCODE": "REF",
    "AREACODE": String,  // Unique to REF collection
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String
}
```

### Key Differentiators from Standard Audit Logging
1. **AREACODE Field**: Provides granular categorization within referral system
2. **Specialized Collection**: `auditLog_REF` vs standard `auditLog`
3. **Area-Specific Filtering**: Admin interface supports filtering by functional area
4. **Enhanced Change Tracking**: JSON changes array for complex updates

## Admin Interface Implementation

### Referral Audit Log Viewer
**File**: `membercentral/model/admin/referrals/dsp_auditLog.cfm`

**Features**:
- DataTables with server-side processing
- Date range filtering
- Area-specific filtering (PANELS, MAINSETTINGS, etc.)
- Real-time search capabilities
- Responsive design with Bootstrap styling

**Filter Options**:
```html
<select name="fArea" id="fArea" class="form-control">
    <option value="PANELS">Panels</option>
    <optgroup label="Referral Settings">
        <option value="MAINSETTINGS">Main Settings</option>
        <option value="FEETYPES">Fee Types</option>
        <option value="CLASSIFICATIONS">Classifications</option>
        <option value="AGENCIES">Agencies</option>
        <option value="SOURCES">Sources</option>
        <option value="STATUSES">Statuses</option>
        <option value="SURVEYTYPES">Survey Types</option>
        <option value="LANGUAGES">Languages</option>
    </optgroup>
    <option value="SCHEDJOBS">Scheduled Jobs</option>
</select>
```

### ColdFusion Handler Integration
**File**: `membercentral/model/admin/referrals/ReferralsAdmin.cfc`

```coldfusion
<cffunction name="listAuditLog" access="public" output="false" returntype="struct">
    <cfargument name="Event" type="any" />
    <cfscript>
        local.referralsAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=referralsJSON&meth=getreferralsAuditLog&mode=stream&referralID=#this.referralID#";
        
        appendBreadCrumbs(arguments.event,{ link='', text='Audit Log' });
    </cfscript>
    
    <cfsavecontent variable="local.data">
        <cfinclude template="dsp_auditLog.cfm">
    </cfsavecontent>

    <cfreturn returnAppStruct(local.data,"echo")>
</cffunction>
```

## Implementation Dependencies and Integration Points

### Custom Field Integration
**Files**: 
- `database/membercentral/migrations/2024/2024-04/MCDEV-7325 - Log to AuditLog When Adding And Removing Panel Questions and Answers.sql`
- `database/membercentral/schema/memberCentral/procedures/cf_auditLogAddField.sql`
- `database/membercentral/schema/memberCentral/procedures/cf_auditLogAddFieldValue.sql`

**Integration Pattern**:
```sql
IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
    IF @controllingResourceType = 'ReferralPanel'
        SELECT @msgjson = 'New Subpanel Question ' + QUOTENAME(@fieldText) + ' has been added for Panel ' + QUOTENAME([name]) + '.'
        FROM dbo.ref_panels
        WHERE siteResourceID = @controllingSiteResourceID;
    ELSE    
        SET @msgjson = 'New Question ' + QUOTENAME(@fieldText) + ' has been added for Panel Selections.';

    EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
        @enteredByMemberID=@enteredByMemberID;
END
```

## Business Rationale Analysis

### Legal Compliance Requirements
- **Client Confidentiality**: Track access and modifications to client referral data
- **Regulatory Compliance**: Maintain audit trails for legal referral service operations
- **Administrative Oversight**: Monitor panel configurations and fee structures

### Operational Benefits
- **Troubleshooting**: Identify when and why referral system issues occurred
- **Change Management**: Track the impact of configuration modifications
- **Quality Assurance**: Monitor referral setup and maintenance activities

### Support Enhancement
- **Issue Resolution**: Quickly identify the source of referral system problems
- **Change History**: Provide detailed history for support inquiries
- **Administrative Accountability**: Track who made specific changes and when

## Technical Patterns Established

### AREACODE Categorization
- Provides granular filtering within referral audit logs
- Enables area-specific audit log viewing
- Supports modular audit log organization

### Enhanced Change Detection
- Before/after comparison using temporary tables
- JSON changes array for complex updates
- Specialized procedures for different data types

### Specialized Collection Usage
- `auditLog_REF` for standard referral audit logging
- `historyEntries_SYS_ADMIN_REFERRALUPDATE` for client referral updates
- Area-specific filtering and organization
