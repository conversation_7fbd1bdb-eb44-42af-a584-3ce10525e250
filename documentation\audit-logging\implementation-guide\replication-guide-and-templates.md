# Audit Logging Replication Guide and Templates

## Overview
This guide provides step-by-step instructions and code templates for implementing audit logging in new functional areas of MemberCentral, based on established patterns from existing implementations.

## Implementation Decision Tree

### 1. Determine Audit Logging Approach
**Question**: What type of audit logging does your module need?

**Simple Audit Logging** (Standard `auditLog` collection):
- Basic CRUD operations
- Single entity changes
- No complex categorization needed
- Examples: Basic member updates, simple configuration changes

**Area-Categorized Audit Logging** (Specialized collection with AREACODE):
- Multiple functional areas within module
- Need for granular filtering
- Complex module with sub-components
- Examples: Referrals (PANELS, SETTINGS), SeminarWeb (program types)

**Context-Enhanced Audit Logging** (Collection with additional context fields):
- Complex relationships between entities
- Need for cross-reference tracking
- Bulk operations and imports
- Examples: Subscriptions (SUBKEYMAP), Complex workflows

### 2. Choose Collection Strategy
```
Simple → auditLog collection
Area-Categorized → auditLog_[MODULE] collection with AREACODE
Context-Enhanced → auditLog_[MODULE] collection with custom fields
```

## Step-by-Step Implementation Guide

### Phase 1: Planning and Design

#### Step 1.1: Define Audit Requirements
**Checklist**:
- [ ] Identify all operations that need audit logging
- [ ] Determine required audit detail level
- [ ] Define user roles who need audit log access
- [ ] Establish retention requirements
- [ ] Document compliance requirements

#### Step 1.2: Choose AUDITCODE and Collection
**Template for AUDITCODE Selection**:
```
Module: [MODULE_NAME]
AUDITCODE: [3-8 character code, typically module abbreviation]
Collection: auditLog_[MODULE] (if specialized) or auditLog (if standard)
Areas: [List of AREACODE values if applicable]
```

**Examples**:
- Referrals: AUDITCODE="REF", Collection="auditLog_REF", Areas=PANELS,SETTINGS,etc.
- Custom Fields: AUDITCODE="MEMCF", Collection="auditLog", Areas=N/A
- Subscriptions: AUDITCODE="SUBSETUP", Collection="auditLog_SUBSETUP", Areas=SUBTYPE,RATE,etc.

### Phase 2: Database Implementation

#### Step 2.1: Create Audit Log Insertion Procedure
**Template**: `[module]_insertAuditLog.sql`
```sql
USE membercentral;
GO

CREATE PROCEDURE dbo.[module]_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100) = NULL,  -- Include if using AREACODE
@msgjson varchar(max),
@[customField]JSON varchar(max) = NULL,  -- Include if using custom context
@isImport bit = 0,  -- Include if supporting import operations
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    
    -- Add import prefix if applicable
    IF @isImport = 1
        SET @msgjson = '[Import] ' + @msgjson;

    -- Choose appropriate collection and structure
    INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
    VALUES ('{ "c":"[COLLECTION_NAME]", "d": {
        "AUDITCODE":"[AUDIT_CODE]",
        [AREACODE_LINE]
        "ORGID":' + cast(@orgID as varchar(10)) + ',
        "SITEID":' + cast(@siteID as varchar(10)) + ',
        "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
        "ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
        "MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '"
        [CUSTOM_FIELDS]
        } }');

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
GO
```

**Substitution Guide**:
- `[module]`: Replace with module name (e.g., `ref`, `sub`, `sw`)
- `[COLLECTION_NAME]`: Replace with collection name (e.g., `auditLog_REF`)
- `[AUDIT_CODE]`: Replace with chosen audit code (e.g., `REF`)
- `[AREACODE_LINE]`: Include `"AREACODE":"' + @areaCode + '",` if using areas
- `[CUSTOM_FIELDS]`: Include custom fields like `,"SUBKEYMAP":' + @subKeyMapJSON + '`

#### Step 2.2: Create MongoDB Model (ColdFusion)
**Template**: `models/system/platform/mongo/auditLog_[MODULE].cfc`
```coldfusion
component name="auditLog_[MODULE]" extends="cbmongodb.models.ActiveEntity" collection="AuditLog_[MODULE]" database="membercentralAudit" accessors=false {
    property name="ORGID" schema=true validate="numeric";
    property name="SITEID" schema=true validate="numeric";
    property name="AUDITCODE" schema=true validate="string";
    property name="AREACODE" schema=true validate="string";  // Include if using AREACODE
    property name="ACTORMEMBERID" schema=true validate="numeric";
    property name="ACTIONDATE" schema=true validate="date";
    property name="MESSAGE" schema=true validate="string";
    // Add custom properties as needed
    property name="[CUSTOM_FIELD]" schema="true" validate="struct";  // For JSON fields
}
```

### Phase 3: Integration with Existing Procedures

#### Step 3.1: Modify Existing CRUD Procedures
**Template for Adding Audit Logging**:
```sql
ALTER PROC dbo.[existing_procedure]
-- ... existing parameters ...
@recordedByMemberID INT  -- Add this parameter

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    -- ... existing logic ...

    -- Add audit logging before RETURN 0
    DECLARE @msgjson VARCHAR(MAX);
    
    -- Simple message example
    SET @msgjson = '[Entity] [' + @entityName + '] has been [action].';
    
    -- OR Complex change detection example
    IF EXISTS(SELECT 1 FROM #tmpChanges) BEGIN
        SET @msgjson = '[Entity] updated. The following changes have been made:';
        SELECT @msgjson = COALESCE(@msgjson + CHAR(13) + CHAR(10), '') + changeDescription
        FROM #tmpChanges;
    END

    -- Insert audit log
    EXEC dbo.[module]_insertAuditLog 
        @orgID=@orgID, 
        @siteID=@siteID, 
        @areaCode='[AREA]',  -- Include if using areas
        @msgjson=@msgjson,
        @enteredByMemberID=@recordedByMemberID;

    RETURN 0;

END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
```

### Phase 4: Admin Interface Implementation

#### Step 4.1: Create Admin Navigation
**Template**: Migration script for admin navigation
```sql
USE membercentral;
GO

DECLARE @level2NavigationID INT, @level3NavID INT, @toolTypeID INT, @resourceTypeID INT, @resourceTypeFunctionID INT;

-- Find parent navigation
SELECT @level2NavigationID = navigationID 
FROM dbo.admin_navigation 
WHERE navName = '[Parent Module Name]' AND parentNavigationID IS NOT NULL;

BEGIN TRY
    BEGIN TRAN;
        
        SELECT @toolTypeID = dbo.fn_getAdminToolTypeID('[ModuleAdmin]');
        SELECT @resourceTypeID = dbo.fn_getResourceTypeID('[ModuleAdmin]');
    
        EXEC dbo.createAdminNavigation 
            @navName='Audit Log', 
            @navDesc='[Module] Audit Log', 
            @parentNavigationID=@level2NavigationID,
            @navAreaID=3, 
            @cfcMethod='get[Module]AuditLogs', 
            @isHeader=0, 
            @showInNav=1, 
            @helpLink='', 
            @iconClasses='', 
            @navigationID=@level3NavID OUTPUT;

        SELECT @resourceTypeFunctionID = dbo.fn_getResourceTypeFunctionID(@resourceTypeID,dbo.fn_getResourceFunctionID('[appropriate_function]',@resourceTypeID));
        EXEC dbo.createAdminFunctionsDeterminingNav @resourceTypeFunctionID=@resourceTypeFunctionID, @toolTypeID=@toolTypeID, @navigationID=@level3NavID;

    COMMIT TRAN;
END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
```

#### Step 4.2: Create Admin Interface
**Template**: `dsp_[module]AuditLog.cfm`
```html
<cfsavecontent variable="local.auditLogsJS">
    <cfoutput>
    <script type="text/javascript">
        let [module]AuditLogsTable;
        function init[Module]AuditLogTable() {
            [module]AuditLogsTable = $('#[module]AuditLogsTable').DataTable({
                "processing": true,
                "serverSide": true,
                "paging": false,
                "info": false,
                "searching": false,
                "language": {
                    "lengthMenu": "_MENU_"
                },
                "ajax": {
                    "url": "#local.[module]AuditLogLink#",
                    "type": "post",
                    "data": function(d) {
                        $.each($('##frmAuditLogsFilter').serializeArray(),function() {
                            d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
                        });
                    }
                },
                "autoWidth": false,
                "columns": [
                    { "data": null,
                        "render": function ( data, type, row, meta ) {
                            return type === 'display' ? '<div class="font-weight-bold">'+data.date+' CT</div><div class="font-italic">'+data.actor+'</div>' : data;
                        },
                        "width": "20%",
                        "className": "align-top"
                    },
                    { "data": "description", "width": "80%", "className": "align-top" }
                ],
                "ordering": false
            });
        }
        function filter[Module]AuditLogs() {
            if ($('##divFilterAuditLogsForm').hasClass('d-none')) {
                $('##divFilterAuditLogsForm').removeClass('d-none');
            }
        }
        function doFilter[Module]AuditLogs() {
            [module]AuditLogsTable.draw();
        }
        function clear[Module]AuditLogFilters() { 
            $('##frmAuditLogsFilter')[0].reset();
            doFilter[Module]AuditLogs(); 
        }
        $(function() {
            init[Module]AuditLogTable();
            mca_setupDatePickerRangeFields('fDateFrom','fDateTo');
            mca_setupCalendarIcons('frmAuditLogsFilter');
        });
    </script>
    </cfoutput>
</cfsavecontent>

<cfhtmlhead text="#local.auditLogsJS#">

<cfoutput>
<h5>[Module] Audit Log</h5>
<div class="toolButtonBar">
    <div><a href="javascript:filter[Module]AuditLogs();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter audit log."><i class="fa-regular fa-filter"></i> Filter Audit Log</a></div>
</div>
<div id="divFilterAuditLogsForm" class="d-none">
    <form name="frmAuditLogsFilter" id="frmAuditLogsFilter" onsubmit="doFilter[Module]AuditLogs();return false;">
        <div class="card card-box mb-3">
            <div class="card-header py-1 bg-light">
                <div class="card-header--title font-weight-bold font-size-md">
                    Filter Audit Log
                </div>
            </div>
            <div class="card-body pb-3">
                <div class="form-row">
                    <div class="col-4 pr-4">
                        <div class="form-group">
                            <div class="form-label-group mb-2">
                                <input type="text" name="fDateFrom" id="fDateFrom" class="form-control" placeholder="Date From">
                                <div class="input-group-append">
                                    <span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
                                </div>
                                <label for="fDateFrom">Date From</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-4 pr-4">
                        <div class="form-group">
                            <div class="form-label-group mb-2">
                                <input type="text" name="fDateTo" id="fDateTo" class="form-control" placeholder="Date To">
                                <div class="input-group-append">
                                    <span class="input-group-text"><a href="javascript:mca_clearDateRangeField('fDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
                                </div>
                                <label for="fDateTo">Date To</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="form-label-group mb-2">
                            <select name="fArea" id="fArea" class="form-control">
                                <option value="">All Areas</option>
                                <!-- Add module-specific area options -->
                                <option value="[AREA1]">[Area 1 Display Name]</option>
                                <option value="[AREA2]">[Area 2 Display Name]</option>
                            </select>
                            <label for="fArea">Area</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer p-2 text-right">
                <button type="button" name="btnClear" class="btn btn-sm btn-secondary" onclick="clear[Module]AuditLogFilters();">Clear Filters</button>
                <button type="submit" class="btn btn-sm btn-primary">Filter Audit Log</button>
            </div>
        </div>
    </form>
</div>
<div class="mb-2">This data describes changes made to [module] settings on this site in a date range.</div>
<table id="[module]AuditLogsTable" class="table table-sm table-striped table-bordered" style="width:100%">
    <thead>
        <tr>
            <th>DATE / UPDATED BY</th>
            <th>DESCRIPTION</th>
        </tr>
    </thead>
</table>
</cfoutput>
```

### Phase 5: Testing and Validation

#### Step 5.1: Testing Checklist
- [ ] Audit log insertion works for all CRUD operations
- [ ] Messages are properly formatted and readable
- [ ] Admin interface displays audit logs correctly
- [ ] Filtering works for all implemented filters
- [ ] Performance is acceptable with large datasets
- [ ] Error handling works properly
- [ ] Permissions are correctly enforced

#### Step 5.2: Validation Procedures
1. **Functional Testing**: Perform operations and verify audit logs are created
2. **Message Quality**: Review audit messages for clarity and completeness
3. **Performance Testing**: Test with large datasets
4. **Security Testing**: Verify access controls and data sanitization
5. **Integration Testing**: Ensure compatibility with existing systems

## Quick Reference Templates

### Simple Audit Log Call
```sql
EXEC dbo.[module]_insertAuditLog 
    @orgID=@orgID, 
    @siteID=@siteID, 
    @msgjson='Simple audit message',
    @enteredByMemberID=@memberID;
```

### Complex Change Detection
```sql
EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;
IF ISNULL(@msg,'') <> '' BEGIN
    SET @msg = 'Entity updated.' + CHAR(13) + CHAR(10) + 'Changes:' + CHAR(13) + CHAR(10) + @msg;
    EXEC dbo.[module]_insertAuditLog @orgID=@orgID, @siteID=@siteID, @msgjson=@msg, @enteredByMemberID=@memberID;
END
```

### Area-Specific Audit Log
```sql
EXEC dbo.[module]_insertAuditLog 
    @orgID=@orgID, 
    @siteID=@siteID, 
    @areaCode='[SPECIFIC_AREA]',
    @msgjson=@message,
    @enteredByMemberID=@memberID;
```

## Common Pitfalls and Solutions

### 1. Message Sanitization
**Problem**: Special characters breaking JSON
**Solution**: Always use `dbo.fn_cleanInvalidXMLChars()` and `REPLACE(@msg,'"','\"')`

### 2. Performance Issues
**Problem**: Audit logging slowing down operations
**Solution**: Use queue-based processing, avoid complex queries in audit procedures

### 3. Missing Context
**Problem**: Audit messages lack sufficient detail
**Solution**: Include entity names, IDs, and relevant context in messages

### 4. Inconsistent Patterns
**Problem**: Different audit message formats across modules
**Solution**: Follow established patterns and use templates consistently
