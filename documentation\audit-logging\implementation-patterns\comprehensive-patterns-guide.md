# MemberCentral Audit Logging Implementation Patterns

## Overview
This document provides comprehensive patterns and methodologies for implementing audit logging across MemberCentral modules, based on analysis of five major audit logging epics.

## Core Architecture Patterns

### 1. Queue-Based MongoDB Architecture
**Pattern**: Hybrid SQL Server + MongoDB with queue-based processing
```sql
-- Standard audit log insertion pattern
INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
VALUES ('{ "c":"[COLLECTION_NAME]", "d": {
    "AUDITCODE":"[AUDIT_CODE]",
    "ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
    "SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
    "ACTORMEMBERID":' + CAST(@memberID AS VARCHAR(20)) + ',
    "ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
    "MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@message),'"','\"') + '" } }');
```

**Key Components**:
- **Queue Table**: `platformQueue.dbo.queue_mongo`
- **MongoDB Database**: `membercentralAudit`
- **Processing**: Queue-based message processing system
- **Collections**: Specialized collections per module/function

### 2. Standardized Stored Procedure Patterns
**Template**: Module-specific audit log insertion procedures
```sql
CREATE PROCEDURE dbo.[module]_insertAuditLog
@orgID int,
@siteID int,
@areaCode varchar(100),
@msgjson varchar(max),
@[moduleSpecificParam] varchar(max) = NULL,
@isImport bit = 0,
@enteredByMemberID int

AS
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    IF @isImport = 1
        SET @msgjson = '[Import] ' + @msgjson;

    INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
    VALUES ('{ "c":"[COLLECTION]", "d": {
        "AUDITCODE":"[CODE]",
        "AREACODE":"' + @areaCode + '",
        "ORGID":' + cast(@orgID as varchar(10)) + ',
        "SITEID":' + cast(@siteID as varchar(10)) + ',
        "ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
        "ACTIONDATE":"' + convert(varchar(20),getdate(),120) + '",
        "MESSAGE":"' + @msgjson + '",
        "[MODULE_SPECIFIC_FIELD]":' + @moduleSpecificParam + ' } }');

    RETURN 0;
END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
```

## AUDITCODE Classification System

### Standard Audit Codes
| Code | Module | Collection | Purpose |
|------|--------|------------|---------|
| SW | SeminarWeb | auditLog | Seminar program changes |
| REF | Referrals | auditLog_REF | Referral system changes |
| SUBSETUP | Subscriptions | auditLog_SUBSETUP | Subscription setup changes |
| MEMCF | Member Custom Fields | auditLog | Custom field operations |
| MFS | Member Field Sets | auditLog | Field set operations |
| SPNSR | Sponsors | auditLog | Sponsor associations |
| RPT | Reports | auditLog | Report operations |
| CFD | Custom Fields | auditLog | Context-specific custom fields |

### AREACODE Categorization Patterns
**Referrals (REF)**:
- PANELS, MAINSETTINGS, FEETYPES, CLASSIFICATIONS, AGENCIES, SOURCES, STATUSES, SURVEYTYPES, LANGUAGES, SCHEDJOBS

**Subscriptions (SUBSETUP)**:
- SUBTYPE, SUBSCRIPTION, SUBSET, RATESCH, RATE

**SeminarWeb (SW)**:
- Program-specific areas based on seminar types (SWL, SWOD, SWB, SWTL)

## Change Detection Patterns

### 1. Simple Message Pattern
**Use Case**: Basic operations (create, delete, simple updates)
```sql
DECLARE @msgjson VARCHAR(MAX);
SET @msgjson = 'New [Entity] [' + @entityName + '] has been created.';

EXEC dbo.[module]_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='[AREA]', 
    @msgjson=@msgjson, @enteredByMemberID=@memberID;
```

### 2. Before/After Comparison Pattern
**Use Case**: Complex updates with multiple field changes
```sql
-- Create temporary audit table
CREATE TABLE #tmpAuditLogData ([rowCode] varchar(10), [Field1] varchar(max), [Field2] varchar(max));

-- Capture old values
INSERT INTO #tmpAuditLogData ([rowCode], [Field1], [Field2])
SELECT 'OLDVAL', field1, field2 FROM [table] WHERE id = @id;

-- Capture new values
INSERT INTO #tmpAuditLogData ([rowCode], [Field1], [Field2])
SELECT 'NEWVAL', @newField1, @newField2;

-- Generate change message
EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

-- Audit log if changes detected
IF ISNULL(@msg,'') <> '' BEGIN
    SET @msg = '[Entity] updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;
    EXEC dbo.[module]_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='[AREA]', 
        @msgjson=@msg, @enteredByMemberID=@memberID;
END
```

### 3. Detailed Change Tracking Pattern
**Use Case**: Complex entities with detailed change descriptions
```sql
CREATE TABLE #tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

-- Individual change detection
INSERT INTO #tmpLogMessages(msg)
SELECT 'Field Name changed from [' + oldValue + '] to [' + @newValue + '].'
FROM [existingData]
WHERE oldValue <> @newValue;

-- Consolidate messages
IF EXISTS(SELECT 1 FROM #tmpLogMessages) BEGIN
    SET @msgjson = @baseMessage + @crlf + 'The following changes have been made:';
    
    SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + dbo.fn_cleanInvalidXMLChars(msg)
    FROM #tmpLogMessages WHERE msg IS NOT NULL;
END
```

## MongoDB Collection Patterns

### 1. Standard Collection (auditLog)
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt,
    "AUDITCODE": String,
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String
}
```

### 2. Area-Specific Collection (auditLog_REF)
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt,
    "AUDITCODE": String,
    "AREACODE": String,  // Additional categorization
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String
}
```

### 3. Context-Enhanced Collection (auditLog_SUBSETUP)
```javascript
{
    "ORGID": NumberInt,
    "SITEID": NumberInt,
    "AUDITCODE": String,
    "AREACODE": String,
    "ACTORMEMBERID": NumberInt,
    "ACTIONDATE": ISODate,
    "MESSAGE": String,
    "SUBKEYMAP": {  // Context-specific data
        "TYPEID": NumberInt,
        "SUBSCRIPTIONID": NumberInt,
        // Additional context fields
    }
}
```

## Admin Interface Patterns

### 1. Standard DataTables Implementation
```javascript
let auditLogsTable = $('#auditLogsTable').DataTable({
    "processing": true,
    "serverSide": true,
    "paging": false,
    "info": false,
    "searching": false,
    "ajax": {
        "url": "#auditLogLink#",
        "type": "post",
        "data": function(d) {
            $.each($('#frmAuditLogsFilter').serializeArray(),function() {
                d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
            });
        }
    },
    "columns": [
        { "data": null, "render": function(data, type, row, meta) {
            return type === 'display' ? 
                '<div class="font-weight-bold">'+data.date+' CT</div><div class="font-italic">'+data.actor+'</div>' : data;
        }, "width": "20%", "className": "align-top" },
        { "data": "description", "width": "80%", "className": "align-top" }
    ],
    "ordering": false
});
```

### 2. Standard Filter Form Pattern
```html
<form name="frmAuditLogsFilter" id="frmAuditLogsFilter" onsubmit="doFilterAuditLogs();return false;">
    <div class="card card-box mb-3">
        <div class="card-header py-1 bg-light">
            <div class="card-header--title font-weight-bold font-size-md">Filter Audit Log</div>
        </div>
        <div class="card-body pb-3">
            <div class="form-row">
                <!-- Date Range Fields -->
                <div class="col-4 pr-4">
                    <div class="form-label-group mb-2">
                        <input type="text" name="fDateFrom" id="fDateFrom" class="form-control" placeholder="Date From">
                        <label for="fDateFrom">Date From</label>
                    </div>
                </div>
                <div class="col-4 pr-4">
                    <div class="form-label-group mb-2">
                        <input type="text" name="fDateTo" id="fDateTo" class="form-control" placeholder="Date To">
                        <label for="fDateTo">Date To</label>
                    </div>
                </div>
                <!-- Area/Category Filter -->
                <div class="col-4">
                    <div class="form-label-group mb-2">
                        <select name="fArea" id="fArea" class="form-control">
                            <option value="">All Areas</option>
                            <!-- Module-specific options -->
                        </select>
                        <label for="fArea">Area</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer p-2 text-right">
            <button type="button" class="btn btn-sm btn-secondary" onclick="clearAuditLogFilters();">Clear Filters</button>
            <button type="submit" class="btn btn-sm btn-primary">Filter Audit Log</button>
        </div>
    </div>
</form>
```

## Error Handling Patterns

### Standard Error Handling
```sql
SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    -- Audit logging logic
    RETURN 0;
END TRY
BEGIN CATCH
    IF @@trancount > 0 ROLLBACK TRANSACTION;
    EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
    RETURN -1;
END CATCH
```

### Message Sanitization
```sql
-- Always sanitize messages before JSON insertion
SET @msgjson = REPLACE(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"');

-- For complex JSON structures, use STRING_ESCAPE
SET @msgjson = STRING_ESCAPE(@msgjson,'json');
```

## Integration Patterns

### 1. Existing Procedure Integration
```sql
-- Add audit logging to existing procedures
ALTER PROC dbo.existing_procedure
-- ... existing parameters ...
@recordedByMemberID INT  -- Add audit parameter

AS
-- ... existing logic ...

-- Add audit logging at end
EXEC dbo.[module]_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='[AREA]', 
    @msgjson=@auditMessage, @enteredByMemberID=@recordedByMemberID;
```

### 2. Custom Field Integration
```sql
-- Integration with custom field audit logging
IF @resourceType = '[ModuleName]' AND @areaName = '[AreaName]' BEGIN
    -- Generate module-specific audit message
    SET @msgjson = 'Module-specific audit message';
    
    EXEC dbo.[module]_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='[AREA]', 
        @msgjson=@msgjson, @enteredByMemberID=@enteredByMemberID;
END
```

## Best Practices

### 1. Message Construction
- Use descriptive, user-friendly messages
- Include entity names in square brackets: `[EntityName]`
- Use consistent terminology across modules
- Include relevant context (IDs, names, relationships)

### 2. Performance Considerations
- Use queue-based processing for non-blocking audit logging
- Implement proper indexing on MongoDB collections
- Use server-side processing for admin interfaces
- Limit audit log retention based on compliance requirements

### 3. Security and Compliance
- Always include actor identification (ACTORMEMBERID)
- Use consistent timestamp formats (ISO 8601)
- Sanitize all user input before logging
- Implement proper access controls for audit log viewing

### 4. Maintenance and Monitoring
- Regular cleanup of old audit log entries
- Monitor queue processing performance
- Implement alerting for audit logging failures
- Regular validation of audit log completeness
