# SEMWEB-1171 Epic Analysis: Seminar Web Audit Logging

## Epic Overview
**Epic ID**: SEMWEB-1171  
**Title**: Log to AuditLog When SeminarWeb Programs Change  
**Business Rationale**: Provide comprehensive audit trails for all SeminarWeb program changes to support compliance, troubleshooting, and administrative oversight.

## Child Stories Implementation Analysis

### SEMWEB-1176: Log to AuditLog When Sponsors are Associated to SeminarWeb Programs
**Implementation Date**: November 2020  
**Files Modified**:
- `database/membercentral/migrations/2020/2020-11/SEMWEB-1176 - Log to AuditLog When Sponsors are Associated to SeminarWeb Programs.sql`

**Technical Implementation**:
- **AUDITCODE**: "SPNSR"
- **Collection**: Standard `auditLog` collection
- **Stored Procedures Modified**:
  - `sponsors_moveSponsorUp`
  - `sponsors_moveSponsorDown` 
  - `sponsors_associateSponsor`
  - `sponsors_deassociateSponsor`

**Audit Events Tracked**:
- Sponsor association to programs: `"[SponsorName] was associated to ReferenceType-ReferenceID."`
- Sponsor deassociation: `"[SponsorName] was deassociated with ReferenceType-ReferenceID."`
- Sponsor reordering: `"[SponsorName] moved up/down to associated ReferenceType-ReferenceID."`

**Implementation Pattern**:
```sql
INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
SELECT '{ "c":"auditLog", "d": {
    "AUDITCODE":"SPNSR",
    "ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
    "SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
    "ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
    "ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
    "MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('[' + sponsorName + '] was associated to ' + @referenceType + '-' + cast(@referenceID AS VARCHAR(20)) + '.'),'"','\"') + '" } }'
FROM dbo.sponsors
WHERE sponsorID = @sponsorID;
```

### SEMWEB-1177: Log to AuditLog When SWL Program Details Change
**Implementation Date**: November 2020  
**Files Modified**:
- `database/membercentral/migrations/2020/2020-11/SEMWEB-1177 - Log to AuditLog When SWL Program Details Change.sql`

**Technical Implementation**:
- **AUDITCODE**: "SW"
- **Collection**: Standard `auditLog` collection
- **Stored Procedures Created**: `swl_updateSeminar`

**Audit Events Tracked**:
- Program name changes
- Subtitle modifications
- Description updates
- Schedule changes (start/end dates)
- Settings modifications (published status, certificates, etc.)
- Pricing changes (DVD prices)
- Feature toggles (NATLE, On-Demand offerings)

**Implementation Pattern**:
- Uses temporary tables to capture before/after states
- Generates detailed change messages for each modified field
- Consolidates all changes into a single audit log entry

**Change Detection Logic**:
```sql
INSERT INTO #tmpLogMessages(msg)
SELECT 'Seminar Name changed from [' + seminarName + '] to [' + @seminarName + '].'
FROM #tblExistingSeminar
WHERE seminarID = @seminarID
AND seminarName <> @seminarName;
```

### SEMWEB-1192: Log to AuditLog When SWL Registrants Actions Performed
**Implementation Date**: November 2020  
**Files Modified**:
- `database/membercentral/migrations/2020/2020-11/SEMWEB-1192 - Log to AuditLog When SWL Registrants Actions Performed.sql`

**Technical Implementation**:
- **AUDITCODE**: "SW"
- **Collection**: Standard `auditLog` collection
- **Stored Procedures Modified**:
  - `sw_addEnrollmentCredits`
  - `swl_importWebAttendance`
  - `sw_toggleAllowRegistrants`

**Audit Events Tracked**:
- Credit jurisdiction additions for registrants
- Web attendance imports
- Registrant allowance toggles
- Detailed registrant information including member numbers and names

**Registrant Information Pattern**:
```sql
SELECT @registrantName = '[' + FirstName + ' ' + LastName + '] (' + membernumber + ')'
FROM dbo.fn_getEnrolleeMemberData(@enrollmentID,null);

SET @msgjson = 'New credit selection jurisdiction has been added for registrant '+ @registrantName +' on '+ @programType + '-' + CAST(@seminarID AS VARCHAR(10))
```

### SEMWEB-1193: Log to AuditLog When SWOD Registrants Actions Performed
**Implementation Date**: November 2020  
**Files Modified**:
- `database/membercentral/migrations/2020/2020-11/SEMWEB-1193 - Log to AuditLog When SWOD Registrants Actions Performed.sql`

**Technical Implementation**:
- **AUDITCODE**: "SW"
- **Collection**: Standard `auditLog` collection
- **Stored Procedures Modified**:
  - `swod_completeIncompleteExams`
  - `swod_manuallyCompleteFile`
  - `swod_recordCompletion`

**Audit Events Tracked**:
- Exam completion actions
- Manual file completions
- SWOD program completions
- Form response completions

**SWOD-Specific Patterns**:
- Tracks completion status changes
- Records manual administrative interventions
- Captures form response processing

## Implementation Dependencies and Chronology

### Phase 1: Foundation (SEMWEB-1176)
- Established sponsor audit logging patterns
- Introduced AUDITCODE "SPNSR"
- Set precedent for reference-based audit messages

### Phase 2: Program Details (SEMWEB-1177, SEMWEB-1178, SEMWEB-1179)
- Implemented comprehensive program change tracking
- Established before/after comparison patterns
- Introduced detailed change message generation

### Phase 3: Registrant Actions (SEMWEB-1192, SEMWEB-1193)
- Added registrant-specific audit logging
- Implemented member identification patterns
- Established completion tracking mechanisms

### Phase 4: Additional Features (SEMWEB-1183-1191)
- Extended audit logging to rates, credits, speakers, subjects
- Implemented opt-in change tracking
- Added link management audit logging

## Business Rationale Analysis

### Compliance Requirements
- **Regulatory Compliance**: Track all changes to continuing education programs
- **Audit Trail**: Provide complete history of program modifications
- **Administrative Oversight**: Enable monitoring of staff actions

### Operational Benefits
- **Troubleshooting**: Identify when and why program issues occurred
- **Change Management**: Track the impact of program modifications
- **Quality Assurance**: Monitor program setup and maintenance activities

### Support Enhancement
- **Issue Resolution**: Quickly identify the source of program problems
- **Change History**: Provide detailed history for support inquiries
- **Administrative Accountability**: Track who made specific changes and when

## Technical Patterns Established

### Standard Message Format
- Consistent JSON structure across all stories
- Standardized field naming conventions
- Uniform timestamp and actor tracking

### Change Detection Logic
- Temporary table patterns for before/after comparisons
- Detailed field-level change tracking
- Consolidated change message generation

### Error Handling
- Consistent TRY/CATCH blocks
- Standardized error handler usage
- Transaction rollback patterns

### Message Sanitization
- Universal use of `fn_cleanInvalidXMLChars()`
- Consistent quote escaping patterns
- Safe JSON message construction
