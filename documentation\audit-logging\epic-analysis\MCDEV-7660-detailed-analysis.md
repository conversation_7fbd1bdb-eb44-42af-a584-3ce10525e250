# MCDEV-7660 Epic Analysis: AuditLog Custom Fields

## Epic Overview
**Epic ID**: MCDEV-7660  
**Title**: AuditLog Custom Fields  
**Business Rationale**: Provide comprehensive audit trails for all custom field operations to support compliance, data integrity monitoring, and administrative oversight of member data customizations.

## Child Stories Implementation Analysis

### MCDEV-7798: Log to AuditLog When Adding Custom Fields
**Implementation Date**: April 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-04/MCDEV-7798 - Log to AuditLog When Adding Custom fields.sql`

**Technical Implementation**:
- **AUDITCODE**: "MEMCF" (Member Custom Fields)
- **Collection**: Standard `auditLog` collection
- **Integration Point**: `ams_createMemberDataColumn` stored procedure
- **Scope**: Organization-wide custom field creation tracking

**Audit Events Tracked**:
- New custom field creation with field name
- Data type specification (VARCHAR, INT, DATE, etc.)
- Display type configuration (textbox, dropdown, checkbox, etc.)
- Default value assignments

**Implementation Pattern**:
```sql
-- audit log
SELECT @msgjson = 'New Custom Field ' + QUOTENAME(@columnName) + ' has been created.' + @crlf
    + 'Data stored as: ' + @dataTypeCode + @crlf
    + 'Display field as: ' + @displayTypeCode;

INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
VALUES('{ "c":"auditLog", "d": {
    "AUDITCODE":"MEMCF",
    "ORGID":' + CAST(@orgID AS varchar(10)) + ',
    "SITEID":' + CAST(@siteID AS varchar(10)) + ',
    "ACTORMEMBERID":' + CAST(@recordedByMemberID AS varchar(20)) + ',
    "ACTIONDATE":"' + CONVERT(varchar(20),GETDATE(),120) + '",
    "MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');
```

### MCDEV-7799: Log to AuditLog When Updating Custom Fields
**Implementation Date**: April 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-04/MCDEV-7799- Log to AuditLog When Updating Custom fields.sql`

**Technical Implementation**:
- **AUDITCODE**: "MEMCF"
- **Collection**: Standard `auditLog` collection
- **Integration Point**: `ams_updateMemberDataColumn` stored procedure
- **Advanced Feature**: Before/after change detection using `ams_getAuditLogMsg`

**Audit Events Tracked**:
- Field name changes
- Description modifications
- Data type changes
- Display type updates
- Validation rule changes (min/max values, character limits)
- Default value modifications
- Permission changes (read-only, allow null, etc.)

**Advanced Change Detection Pattern**:
```sql
-- Create temporary audit table for before/after comparison
CREATE TABLE #tmpAuditLogData ([rowCode] varchar(10), [Field Name] varchar(max), [Description] varchar(max), 
    [Data Type] varchar(max), [Display Type] varchar(max), [Allow Null] varchar(max), [Default Value] varchar(max),
    [Allow Multiple] varchar(max), [Is Read Only] varchar(max), [Min Characters] varchar(max), [Max Characters] varchar(max));

-- Capture old values
INSERT INTO #tmpAuditLogData ([rowCode], [Field Name], [Description], [Data Type], [Display Type], [Allow Null], [Default Value], [Allow Multiple], [Is Read Only], [Min Characters], [Max Characters])
SELECT 'OLDVAL', c.columnName, c.columnDesc, dt.dataTypeCode, ds.displayTypeCode, 
    CASE c.allowNull WHEN 1 THEN 'Yes' ELSE 'No' END,
    ISNULL(defV.valueString,''), CASE c.allowMultiple WHEN 1 THEN 'Yes' ELSE 'No' END,
    CASE c.isReadOnly WHEN 1 THEN 'Yes' ELSE 'No' END, CAST(c.minChars AS varchar(10)), CAST(c.maxChars AS varchar(10))
FROM dbo.ams_memberDataColumns AS c
WHERE c.columnID = @columnID;

-- Capture new values
INSERT INTO #tmpAuditLogData ([rowCode], [Field Name], [Description], [Data Type], [Display Type], [Allow Null], [Default Value], [Allow Multiple], [Is Read Only], [Min Characters], [Max Characters])
SELECT 'NEWVAL', @columnName, @columnDesc, @dataTypeCode, @displayTypeCode,
    CASE @allowNull WHEN 1 THEN 'Yes' ELSE 'No' END,
    ISNULL(@defaultValue,''), CASE @allowMultiple WHEN 1 THEN 'Yes' ELSE 'No' END,
    CASE @isReadOnly WHEN 1 THEN 'Yes' ELSE 'No' END, CAST(@minChars AS varchar(10)), CAST(@maxChars AS varchar(10));

-- Generate change message
EXEC dbo.ams_getAuditLogMsg @auditLogTable='#tmpAuditLogData', @msg=@msg OUTPUT;

-- audit log
IF ISNULL(@msg,'') <> '' BEGIN
    SET @msg = 'Custom Field ' + QUOTENAME(@oldColumnName) + ' has been updated.' + @crlf + 'The following changes have been made:' + @crlf + @msg;
    
    INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
    VALUES('{ "c":"auditLog", "d": {
        "AUDITCODE":"MEMCF",
        "ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
        "SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
        "ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
        "ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
        "MESSAGE":"' + REPLACE(dbo.fn_cleanInvalidXMLChars(@msg),'"','\"') + '" } }');
END
```

### MCDEV-7800: Log to AuditLog When Deleting Custom Fields
**Implementation Date**: April 2024  
**Files Modified**:
- `database/membercentral/migrations/2024/2024-04/MCDEV-7800-Log to AuditLog When deleting a Custom field.sql`

**Technical Implementation**:
- **AUDITCODE**: "MEMCF"
- **Collection**: Standard `auditLog` collection
- **Integration Point**: `ams_deleteMemberDataColumn` stored procedure
- **Advanced Feature**: Cascade deletion tracking

**Audit Events Tracked**:
- Single custom field deletion
- Multiple custom field deletion (bulk operations)
- Dependent group condition deletions
- Dependent member field deletions
- Cascade impact reporting

**Bulk Deletion Pattern**:
```sql
DECLARE @tblColumns TABLE (columnName varchar(255));
DECLARE @columnsCount int, @msgjson varchar(max), @crlf varchar(10);

-- Capture field names before deletion
INSERT INTO @tblColumns (columnName)
SELECT columnName 
FROM dbo.ams_memberDataColumns 
WHERE columnID IN (SELECT value FROM dbo.fn_varcharListToTableInline(@columnIDList,','));

SELECT @columnsCount = COUNT(*) FROM @tblColumns;

-- Build deletion message
SELECT @msgjson = COALESCE(@msgjson + ', ', '') + quoteName(columnName)
FROM @tblColumns;

SELECT @msgjson = CASE
    WHEN @columnsCount = 1 THEN 'Custom Field ' + @msgjson + ' has been deleted.'
    WHEN @columnsCount > 1 THEN 'Custom Fields ' + @msgjson + ' have been deleted.'
END;

-- Add cascade deletion information
IF @CIDList IS NOT NULL
    SELECT @msgjson = @msgjson + @crlf + 'Dependent Group Conditions were also deleted.'

IF @fieldCodeList IS NOT NULL
    SELECT @msgjson = @msgjson + @crlf + 'Dependent Member Fields were also deleted.'
```

## Specialized Audit Logging Procedures

### cf_auditLogAddField
**Purpose**: Audit logging for custom field additions in specialized contexts
**File**: `database/membercentral/schema/memberCentral/procedures/cf_auditLogAddField.sql`

**Integration Points**:
- Referral panel custom fields
- Resource-specific custom field additions
- Context-aware audit message generation

**Context-Aware Pattern**:
```sql
IF @resourceType = 'ClientReferrals' AND @areaName = 'ReferralPanelChooser' BEGIN
    IF @controllingResourceType = 'ReferralPanel'
        SELECT @msgjson = 'New Subpanel Question [' + @fieldText + '] has been added for Panel [' + [name] + '].'
        FROM dbo.ref_panels
        WHERE siteResourceID = @controllingSiteResourceID;
    ELSE    
        SET @msgjson = 'New Question [' + @fieldText + '] has been added for Panel Selections.';

    SET @msgjson = @msgjson + CASE @isRequired WHEN 1 THEN @crlf + 'Is Required?: Yes' ELSE '' END;
    SET @msgjson = STRING_ESCAPE(@msgjson,'json');

    EXEC dbo.ref_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='PANELS', @msgjson=@msgjson,
        @enteredByMemberID=@enteredByMemberID;
END
```

### cf_auditLogAddFieldValue
**Purpose**: Audit logging for custom field value additions
**File**: `database/membercentral/schema/memberCentral/procedures/cf_auditLogAddFieldValue.sql`

**Features**:
- Dropdown option additions
- Multi-select value additions
- Context-specific value tracking

## Integration with Member Field Sets

### MFS Audit Code Integration
**AUDITCODE**: "MFS" (Member Field Sets)
**Integration Point**: Member field set operations

**Pattern**:
```sql
INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
SELECT '{ "c":"auditLog", "d": { 
        "AUDITCODE":"MFS", 
        "ORGID":' + cast(@orgID as varchar(10)) + ', 
        "SITEID":' + cast(@siteID as varchar(10)) + ', 
        "ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
        "ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
        "MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars('New field [' + fieldLabel + '(' + fieldCode + ')] added to the field set [' + @fieldSetName + '].'),'"','\"') + '" } }'
FROM dbo.ams_memberFields
WHERE fieldID = @fieldID;
```

## System Integration Points

### View Recreation Triggers
- **ams_createVWMemberData**: Recreates member data views after custom field changes
- **Cache Table Updates**: Maintains data consistency across cached member information
- **Subscription Integration**: Updates member date field references in subscription rules

### Cross-System Dependencies
- **Custom Apps Integration**: Updates scheduled task configurations when date fields are renamed
- **Member Join Date Rules**: Maintains field name consistency in subscription date tracking
- **Import/Export Systems**: Ensures custom field definitions remain synchronized

## Business Rationale Analysis

### Data Integrity Compliance
- **Schema Change Tracking**: Monitor all modifications to member data structure
- **Regulatory Compliance**: Maintain audit trails for member data field modifications
- **Data Governance**: Track who can modify member data structures and when

### Operational Benefits
- **Troubleshooting**: Identify when custom field issues were introduced
- **Change Management**: Track the impact of custom field modifications on member data
- **Quality Assurance**: Monitor custom field setup and maintenance activities

### Support Enhancement
- **Issue Resolution**: Quickly identify the source of custom field problems
- **Change History**: Provide detailed history for support inquiries about member data fields
- **Administrative Accountability**: Track who made specific custom field changes and when

## Technical Patterns Established

### AUDITCODE Specialization
- **"MEMCF"**: Member custom field operations (create, update, delete)
- **"MFS"**: Member field set operations
- **"CFD"**: Custom field data operations (context-specific)

### Advanced Change Detection
- **Before/After Comparison**: Uses temporary tables with OLDVAL/NEWVAL patterns
- **Field-Level Granularity**: Tracks individual property changes within custom fields
- **Cascade Impact Tracking**: Reports dependent deletions and modifications

### Context-Aware Logging
- **Resource Type Detection**: Different audit messages based on where custom fields are used
- **Area-Specific Routing**: Routes audit logs to appropriate collections based on context
- **Integration Point Awareness**: Coordinates with referral, subscription, and other system audit logs

### System Consistency Maintenance
- **View Recreation**: Automatically updates database views after custom field changes
- **Cross-Reference Updates**: Maintains consistency in related system configurations
- **Cache Invalidation**: Ensures cached member data reflects custom field changes
